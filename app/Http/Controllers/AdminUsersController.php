<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class AdminUsersController extends Controller
{
    /**
     * Display a listing of admin users.
     */
    public function index()
    {
        $users = User::role(['admin', 'superadmin'])
            ->with('roles')
            ->orderBy('created_at', 'desc')
            ->get();
        
        return view('admin-users.index', compact('users'));
    }

    /**
     * Show the form for creating a new admin user.
     */
    public function create()
    {
        $roles = Role::whereIn('name', ['admin', 'superadmin'])->get();
        return view('admin-users.create', compact('roles'));
    }

    /**
     * Store a newly created admin user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'role' => 'required|in:admin,superadmin',
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:255',
        ]);

        // Generate a random password
        $password = Str::random(12);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($password),
            'raw_password' => $password,
            'phone' => $request->phone,
            'location' => $request->location,
        ]);

        // Assign role
        $user->assignRole($request->role);

        return redirect()->route('admin-users.index')
            ->with('success', "Utilizatorul admin a fost creat cu succes. Parola generată: {$password}");
    }

    /**
     * Show the form for editing the specified admin user.
     */
    public function edit(User $adminUser)
    {
        // Ensure the user has admin or superadmin role
        if (!$adminUser->hasAnyRole(['admin', 'superadmin'])) {
            return redirect()->route('admin-users.index')
                ->with('error', 'Utilizatorul specificat nu este un administrator.');
        }

        $roles = Role::whereIn('name', ['admin', 'superadmin'])->get();
        return view('admin-users.edit', compact('adminUser', 'roles'));
    }

    /**
     * Update the specified admin user in storage.
     */
    public function update(Request $request, User $adminUser)
    {
        // Ensure the user has admin or superadmin role
        if (!$adminUser->hasAnyRole(['admin', 'superadmin'])) {
            return redirect()->route('admin-users.index')
                ->with('error', 'Utilizatorul specificat nu este un administrator.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . $adminUser->id,
            'role' => 'required|in:admin,superadmin',
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:255',
        ]);

        $adminUser->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'location' => $request->location,
        ]);

        // Update role if changed
        $adminUser->syncRoles([$request->role]);

        return redirect()->route('admin-users.index')
            ->with('success', 'Utilizatorul admin a fost actualizat cu succes.');
    }

    /**
     * Remove the specified admin user from storage.
     */
    public function destroy(User $adminUser)
    {
        // Ensure the user has admin or superadmin role
        if (!$adminUser->hasAnyRole(['admin', 'superadmin'])) {
            return redirect()->route('admin-users.index')
                ->with('error', 'Utilizatorul specificat nu este un administrator.');
        }

        // Prevent deletion of the last superadmin
        if ($adminUser->hasRole('superadmin')) {
            $superadminCount = User::role('superadmin')->count();
            if ($superadminCount <= 1) {
                return redirect()->route('admin-users.index')
                    ->with('error', 'Nu se poate șterge ultimul superadministrator.');
            }
        }

        // Prevent users from deleting themselves
        if ($adminUser->id === auth()->id()) {
            return redirect()->route('admin-users.index')
                ->with('error', 'Nu vă puteți șterge propriul cont.');
        }

        $adminUser->delete();

        return redirect()->route('admin-users.index')
            ->with('success', 'Utilizatorul admin a fost șters cu succes.');
    }

    /**
     * Reset password for the specified admin user.
     */
    public function resetPassword(User $user)
    {
        // Ensure the user has admin or superadmin role
        if (!$user->hasAnyRole(['admin', 'superadmin'])) {
            return redirect()->route('admin-users.index')
                ->with('error', 'Utilizatorul specificat nu este un administrator.');
        }

        // Generate a new random password
        $password = Str::random(12);

        $user->update([
            'password' => Hash::make($password),
            'raw_password' => $password,
        ]);

        return redirect()->route('admin-users.index')
            ->with('success', "Parola a fost resetată cu succes. Noua parolă: {$password}");
    }
}
