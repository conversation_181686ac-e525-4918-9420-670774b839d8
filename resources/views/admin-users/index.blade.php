@extends('layouts.user_type.auth')

@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header bg-gradient-primary">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="text-white">Utilizatori admin</h4>
                            <a href="{{ route('admin-users.create') }}" class="btn btn-light">
                                <i class="fas fa-plus me-1"></i>Adaugă utilizator admin
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                        @if (session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nume</th>
                                        <th>Email</th>
                                        <th>Telefon</th>
                                        <th>Locație</th>
                                        <th>Rol</th>
                                        <th>Creat la</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($users as $user)
                                        <tr>
                                            <td>{{ $user->id }}</td>
                                            <td>
                                                <strong>{{ $user->name }}</strong>
                                                @if($user->id === auth()->id())
                                                    <span class="badge bg-info ms-1">Tu</span>
                                                @endif
                                            </td>
                                            <td>{{ $user->email }}</td>
                                            <td>{{ $user->phone ?? '-' }}</td>
                                            <td>{{ $user->location ?? '-' }}</td>
                                            <td>
                                                @if($user->hasRole('superadmin'))
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-crown me-1"></i>Superadmin
                                                    </span>
                                                @elseif($user->hasRole('admin'))
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-user-shield me-1"></i>Admin
                                                    </span>
                                                @endif
                                            </td>
                                            <td>{{ $user->created_at->format('d.m.Y H:i') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin-users.edit', $user) }}" 
                                                       class="btn btn-sm btn-outline-primary"
                                                       title="Editează">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    
                                                    <form action="{{ route('admin-users.reset-password', $user) }}" 
                                                          method="POST" 
                                                          style="display: inline;"
                                                          onsubmit="return confirm('Sigur doriți să resetați parola pentru {{ $user->name }}?')">
                                                        @csrf
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-outline-warning"
                                                                title="Resetează parola">
                                                            <i class="fas fa-key"></i>
                                                        </button>
                                                    </form>

                                                    @if($user->id !== auth()->id())
                                                        @if(!$user->hasRole('superadmin') || \App\Models\User::role('superadmin')->count() > 1)
                                                            <form action="{{ route('admin-users.destroy', $user) }}" 
                                                                  method="POST" 
                                                                  style="display: inline;"
                                                                  onsubmit="return confirm('Sigur doriți să ștergeți utilizatorul {{ $user->name }}?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                                        title="Șterge">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        @else
                                                            <button class="btn btn-sm btn-outline-secondary" 
                                                                    disabled 
                                                                    title="Nu se poate șterge ultimul superadmin">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        @endif
                                                    @else
                                                        <button class="btn btn-sm btn-outline-secondary" 
                                                                disabled 
                                                                title="Nu vă puteți șterge propriul cont">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">Nu există utilizatori admin.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
