@extends('layouts.user_type.auth')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-gradient-primary">
                    <h4 class="text-white">Actualizați participant</h4>
                </div>
                <div class="card-body">
                        @if (session('success'))
                            <div class="alert alert-success">
                                {{ session('success') }}
                            </div>
                        @endif
                    <form action="{{ route('visitors.update', $visitor->id) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row info-personale">
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="nume" placeholder="Nume" value="{{ old('nume', $visitor->nume) }}">
                                @error('nume') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="prenume" placeholder="Prenume" value="{{ old('prenume', $visitor->prenume) }}">
                                @error('prenume') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="email" name="email" placeholder="Email" value="{{ old('email', $visitor->email) }}">
                                @error('email') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="telefon" placeholder="Telefon" value="{{ old('telefon', $visitor->date->telefon ?? '') }}">
                                @error('telefon') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="adresa_corespondenta" placeholder="Adresă" value="{{ old('adresa_corespondenta', $visitor->date->adresa_corespondenta ?? '') }}">
                                @error('adresa') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>
                            <div class="col-md-4 mt-3">
                                <input class="form-control" type="text" name="regiune" placeholder="Regiune" value="{{ old('regiune', $visitor->regiune ?? '') }}">
                                @error('regiune') <div class="text-danger">{{ $message }}</div> @enderror
                            </div>

                            <!-- Dynamic Extra Fields - Only show fields with values -->
                            @php
                                $visitorData = (array)$visitor->date;
                                $availableFields = \App\Models\VisitorExtraField::getActiveFields();
                            @endphp

                            @foreach($availableFields as $field)
                                @php
                                    $currentValue = old('extra_' . $field->field_key, $visitorData['extra_' . $field->field_key] ?? '');
                                @endphp
                                @if(!empty($currentValue))
                                    <div class="col-md-4 mt-3" data-field-key="{{ $field->field_key }}">
                                        @if($field->field_type === 'select')
                                            <div class="input-group">
                                                <select class="form-control" name="extra_{{ $field->field_key }}" {{ $field->is_required ? 'required' : '' }}>
                                                    <option value="">{{ $field->field_label }}</option>
                                                    @foreach($field->getOptionsArray() as $option)
                                                        <option value="{{ $option }}" {{ $currentValue == $option ? 'selected' : '' }}>
                                                            {{ $option }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <button type="button" class="btn btn-outline-danger btn-sm m-0" onclick="removeExtraField('{{ $field->field_key }}')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        @else
                                            <div class="input-group">
                                                <input class="form-control"
                                                       type="{{ $field->field_type === 'phone' ? 'tel' : $field->field_type }}"
                                                       name="extra_{{ $field->field_key }}"
                                                       placeholder="{{ $field->field_label }}"
                                                       value="{{ $currentValue }}"
                                                       {{ $field->is_required ? 'required' : '' }}>
                                                <button type="button" class="btn btn-outline-danger btn-sm m-0" onclick="removeExtraField('{{ $field->field_key }}')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        @endif
                                        @error('extra_' . $field->field_key) <div class="text-danger">{{ $message }}</div> @enderror
                                    </div>
                                @endif
                            @endforeach

                            <!-- Dynamic Extra Fields Container for new fields -->
                            <div id="extra-fields-container" class="w-100">
                                <!-- New extra fields will be added here dynamically -->
                            </div>

                            <!-- Add Extra Field Button -->
                            <div class="col-md-4 mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="add-extra-field">
                                    <i class="fas fa-plus"></i> Adaugă câmp suplimentar
                                </button>
                            </div>
                        </div>
                        <hr/>
                        <div class="participare-options my-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <select name="categorie" class="form-select select-tip-participare">
                                        <option value="">Selectează categoria</option>
                                        @foreach(\App\Models\BadgeCategory::all() as $category)
                                            <option value="{{ $category->name }}" {{ old('categorie', $visitor->categorie) == $category->name ? 'selected' : '' }}>
                                                {{ $category->display_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('categorie') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Actualizați</button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="card mt-4">
                <div class="card-header bg-gradient-warning d-none">
                    <h4 class="text-white">Generați un badge customizat</h4>
                </div>
                <div class="card-body">
                    <a href="{{ route('visitors.badge', $visitor->id) }}" target="_blank" class="btn btn-info btn-sm my-1">Badge</a>
                    <form class="d-none" action="{{ route('visitors.customBadge') }}" method="POST" target="_blank" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="id_vizitator" value="{{ $visitor->id }}">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="mb-3">
                                    <label for="nume" class="form-label">Nume</label>
                                    <input type="text" name="nume" class="form-control" id="nume" value="{{ old('nume', $visitor->nume) }}">
                                    @error('nume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="mb-3">
                                    <label for="prenume" class="form-label">Prenume</label>
                                    <input type="text" name="prenume" class="form-control" id="prenume" value="{{ old('prenume', $visitor->prenume) }}">
                                    @error('prenume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_nume" class="form-label">Font size nume</label>
                                    <input type="number" name="font_size_nume" class="form-control" id="font_size_nume" value="{{ old('font_size_nume', 20) }}">
                                    @error('font_size_nume') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="logo" class="form-label">Logo</label>
                                    <input type="file" name="logo" class="form-control" id="logo">
                                    @error('logo') <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-10">
                                <div class="mb-3">
                                    <label for="tip" class="form-label">Label</label>
                                    <select name="label" class="form-select" id="tip">
                                        <option value="PARTICIPANT" {{ old('label', $visitor->categorie == 'participant' ? "PARTICIPANT" : '') === 'PARTICIPANT' ? 'selected' : '' }}>PARTICIPANT</option>
                                        <option value="SPEAKER" {{ old('label', $visitor->categorie == 'speaker' ? "SPEAKER" : '') === 'SPEAKER' ? 'selected' : '' }}>SPEAKER</option>
                                        <option value="STAFF" {{ old('label', $visitor->categorie == 'staff' ? "STAFF" : '') === 'STAFF' ? 'selected' : '' }}>STAFF</option>
                                    </select>
                                    @error('tip')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_label" class="form-label">Font size label</label>
                                    <input type="number" name="font_size_label" class="form-control" id="font_size_label" value="{{ old('font_size_label', 16) }}">
                                    @error('font_size_label')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-10">
                                <div class="mb-3">
                                    <label for="regiune" class="form-label">Regiune</label>
                                    <input type="text" name="regiune" class="form-control" id="regiune" value="{{ old('regiune', $visitor->regiune) }}">
                                    @error('regiune')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="font_size_regiune" class="form-label">Font size regiune</label>
                                    <input type="number" name="font_size_regiune" class="form-control" id="font_size_regiune" value="{{ old('font_size_regiune', 12) }}">
                                    @error('font_size_regiune')
                                    <div class="text-danger">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-warning">Generați</button>
                    </form>
                    <div class="mt-3">
                        <img src="{{ $img }}" alt="QR Code" class="w-50"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Available extra fields from database
    const availableFields = @json(\App\Models\VisitorExtraField::getActiveFields());
    const existingFields = @json(array_keys($visitorData ?? []));
    let addedFields = [...existingFields];

    document.addEventListener('DOMContentLoaded', function() {
        const addButton = document.getElementById('add-extra-field');
        const container = document.getElementById('extra-fields-container');

        addButton.addEventListener('click', function() {
            showFieldSelector();
        });

        function showFieldSelector() {
            const unusedFields = availableFields.filter(field => !addedFields.includes(field.field_key));

            if (unusedFields.length === 0) {
                alert('Toate câmpurile disponibile au fost adăugate.');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Selectați câmpul de adăugat</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <select class="form-control" id="field-selector">
                                <option value="">Alegeți un câmp...</option>
                                ${unusedFields.map(field => `<option value="${field.field_key}">${field.field_label}</option>`).join('')}
                            </select>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                            <button type="button" class="btn btn-primary" onclick="addSelectedField()">Adaugă</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }

        window.addSelectedField = function() {
            const selector = document.getElementById('field-selector');
            const fieldKey = selector.value;

            if (!fieldKey) {
                alert('Vă rog să selectați un câmp.');
                return;
            }

            const field = availableFields.find(f => f.field_key === fieldKey);
            addExtraField(field);
            addedFields.push(fieldKey);

            // Close modal
            const modal = selector.closest('.modal');
            bootstrap.Modal.getInstance(modal).hide();
        };

        function addExtraField(field) {
            const fieldDiv = document.createElement('div');
            fieldDiv.className = 'col-md-4 mt-3';
            fieldDiv.setAttribute('data-field-key', field.field_key);

            let inputHtml = '';
            if (field.field_type === 'select') {
                const options = field.field_options || [];
                inputHtml = `
                    <select class="form-control" name="extra_${field.field_key}" ${field.is_required ? 'required' : ''}>
                        <option value="">${field.field_label}</option>
                        ${options.map(option => `<option value="${option}">${option}</option>`).join('')}
                    </select>
                `;
            } else {
                const inputType = field.field_type === 'phone' ? 'tel' : field.field_type;
                inputHtml = `
                    <input class="form-control"
                           type="${inputType}"
                           name="extra_${field.field_key}"
                           placeholder="${field.field_label}"
                           ${field.is_required ? 'required' : ''}>
                `;
            }

            fieldDiv.innerHTML = `
                <div class="input-group">
                    ${inputHtml}
                    <button type="button" class="btn btn-outline-danger btn-sm m-0" onclick="removeExtraField('${field.field_key}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            container.appendChild(fieldDiv);
        }

        window.removeExtraField = function(fieldKey) {
            const fieldDiv = document.querySelector(`[data-field-key="${fieldKey}"]`);
            if (fieldDiv) {
                fieldDiv.remove();
                addedFields = addedFields.filter(key => key !== fieldKey);
            }
        };
    });
</script>
@endsection